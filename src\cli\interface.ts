import * as readline from 'readline';
import chalk from 'chalk';
import ora from 'ora';
import { EventEmitter } from 'events';
import * as path from 'path';
import { Message, ChatSession, ExecutionPlan, CLIConfig } from '../types';
import { ContextManager } from '../context/manager';
import { ModelManager } from '../models/manager';
import { ToolManager } from '../tools/manager';
import { PlanExecutor } from '../core/executor';
import { ErrorDetector } from '../core/error-detector';
import { DiffReviewer } from '../core/diff-reviewer';
import { ModernUI } from './modern-ui';
import { Dashboard } from './dashboard';

export class CLIInterface extends EventEmitter {
  private rl: readline.Interface;
  private contextManager: ContextManager;
  private modelManager: ModelManager;
  private toolManager: ToolManager;
  private planExecutor: PlanExecutor;
  private errorDetector: ErrorDetector;
  private diffReviewer: DiffReviewer;
  private currentSession: ChatSession | null = null;
  private config: CLIConfig;
  private spinner: any;
  private isProcessing: boolean = false;
  private escapeCount: number = 0;
  private escapeTimer: NodeJS.Timeout | null = null;
  private streamingResponse: boolean = false;
  private modernUI: ModernUI;
  private dashboard: Dashboard;
  private startTime: Date;
  private lastActivity: Date;
  private recentCommands: string[] = [];
  private activeOperations: string[] = [];
  private displayMode: 'dashboard' | 'compact' | 'minimal' = 'compact';
  private isInitialized: boolean = false;

  constructor(config: CLIConfig) {
    super();
    this.config = config;
    this.startTime = new Date();
    this.lastActivity = new Date();

    // Initialize modern UI components
    this.modernUI = new ModernUI({
      theme: 'default',
      animations: true,
      compactMode: false,
      showTimestamps: true,
      showProgressBars: true
    });
    this.dashboard = new Dashboard(this.modernUI);

    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: chalk.cyan('kritrima> ')
    });

    this.contextManager = new ContextManager(config.contextSize);
    this.modelManager = new ModelManager();
    this.toolManager = new ToolManager();
    this.planExecutor = new PlanExecutor(this.toolManager);
    this.errorDetector = new ErrorDetector(config.workingDirectory);
    this.diffReviewer = new DiffReviewer();
    this.spinner = ora();

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.rl.on('line', this.handleUserInput.bind(this));
    this.rl.on('close', this.handleExit.bind(this));

    // Handle key presses for double ESC functionality
    process.stdin.on('keypress', this.handleKeyPress.bind(this));
    if (process.stdin.isTTY) {
      process.stdin.setRawMode(true);
    }

    this.planExecutor.on('stepStart', this.handleStepStart.bind(this));
    this.planExecutor.on('stepComplete', this.handleStepComplete.bind(this));
    this.planExecutor.on('planComplete', this.handlePlanComplete.bind(this));

    this.errorDetector.on('errorDetected', this.handleErrorDetected.bind(this));
  }

  private async initializeSession(): Promise<void> {
    try {
      this.currentSession = {
        id: this.generateSessionId(),
        messages: [],
        context: [],
        model: await this.modelManager.getDefaultModel(),
        config: await this.modelManager.getDefaultConfig(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
    } catch (error) {
      // No models available - create a minimal session
      console.log(chalk.yellow('⚠️  No AI models configured. Some features will be limited.'));
      console.log(chalk.gray('   Use the "models" command to see available models.'));
      console.log(chalk.gray('   Use the "model <name>" command to switch models.'));

      this.currentSession = {
        id: this.generateSessionId(),
        messages: [],
        context: [],
        model: { name: 'none', provider: 'none', maxTokens: 0, supportsStreaming: false, supportsFunctionCalling: false } as any,
        config: { model: 'none', temperature: 0.7, maxTokens: 4096 } as any,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }

    // Load initial context from working directory with progress indication
    try {
      await this.loadContextWithProgress(this.config.workingDirectory);
    } catch (error) {
      console.log(chalk.yellow('⚠️  Failed to load initial context. You can add files manually using "context add <path>".'));
    }

    // Start real-time codebase monitoring
    this.startCodebaseMonitoring();
  }

  public async start(): Promise<void> {
    // Display welcome message once
    this.displayWelcome();

    // Initialize session and context
    await this.initializeSession();

    // Mark as initialized and display interface
    this.isInitialized = true;
    this.displayInterface();

    // Start the prompt
    this.updatePrompt();
    this.rl.prompt();
  }

  private displayWelcome(): void {
    // Clear screen for a fresh start
    this.modernUI.clearScreen();

    // Display welcome banner
    const banner = this.modernUI.createBanner('🤖 KRITRIMA AI', 'fancy');
    console.log(banner);
    console.log();
    console.log(chalk.gray('Advanced Local CLI Terminal LLM Interaction Environment'));
    console.log(chalk.gray('Type "help" for available commands or start asking questions!'));
    console.log();
  }

  private displayInterface(): void {
    if (!this.isInitialized || !this.currentSession) {
      return;
    }

    switch (this.displayMode) {
      case 'dashboard':
        this.displayDashboard();
        break;
      case 'compact':
        this.displayCompactHeader();
        break;
      case 'minimal':
        this.displayMinimalHeader();
        break;
    }
  }

  private displayDashboard(): void {
    const dashboardData = this.getDashboardData();
    const dashboardOutput = this.dashboard.render(dashboardData);
    console.log(dashboardOutput);
    console.log();
  }

  private displayCompactHeader(): void {
    const dashboardData = this.getDashboardData();
    const compactOutput = this.dashboard.renderCompact(dashboardData);
    console.log(compactOutput);
    console.log();
    this.displayQuickStatus();
  }

  private displayQuickStatus(): void {
    if (!this.currentSession) {
      console.log(chalk.yellow('⚠️  Session initializing...'));
      return;
    }

    const contextFiles = this.currentSession.context.length || 0;
    const totalTokens = this.contextManager.getTotalTokens();

    const stats = [
      { label: 'Context', value: `${contextFiles} files (${totalTokens.toLocaleString()} tokens)`, color: 'info' as const },
      { label: 'Autonomy', value: this.config.autonomyLevel, color: 'accent' as const }
    ];

    const statusLine = this.modernUI.createStatusLine(stats);
    console.log(statusLine);
  }

  private displayMinimalHeader(): void {
    const dashboardData = this.getDashboardData();
    const minimalOutput = this.dashboard.renderMinimal(dashboardData);
    console.log(minimalOutput);
  }

  private createDynamicPrompt(): string {
    if (!this.currentSession) {
      return chalk.cyan('kritrima> ');
    }

    const contextStatus = this.currentSession.context.length > 0 ? chalk.green('●') : chalk.yellow('○');
    const modelStatus = this.currentSession.model.name !== 'none' ? chalk.green('●') : chalk.red('●');

    return `${contextStatus}${modelStatus} ${chalk.cyan('kritrima')}${chalk.dim('>')} `;
  }

  private getDashboardData() {
    const uptime = Math.floor((Date.now() - this.startTime.getTime()) / 1000);
    const contextFiles = this.currentSession?.context.length || 0;
    const totalTokens = this.contextManager.getTotalTokens();

    let systemStatus: 'healthy' | 'warning' | 'error' = 'healthy';
    if (this.currentSession?.model.name === 'none') {
      systemStatus = 'error';
    } else if (contextFiles === 0) {
      systemStatus = 'warning';
    }

    return {
      session: this.currentSession,
      config: this.config,
      contextFiles,
      totalTokens,
      uptime,
      lastActivity: this.lastActivity,
      systemStatus,
      activeOperations: this.activeOperations,
      recentCommands: this.recentCommands
    };
  }

  private displayStatus(): void {
    if (!this.currentSession) {
      console.log(chalk.yellow('⚠️  Session initializing...'));
      return;
    }

    const dashboardData = this.getDashboardData();
    const statusOutput = this.dashboard.render(dashboardData);
    console.log(statusOutput);
  }

  private async handleUserInput(input: string): Promise<void> {
    const trimmedInput = input.trim();

    if (!trimmedInput) {
      this.rl.prompt();
      return;
    }

    // Update activity tracking
    this.lastActivity = new Date();
    this.recentCommands.unshift(trimmedInput);
    if (this.recentCommands.length > 10) {
      this.recentCommands.pop();
    }

    // Handle special commands
    if (await this.handleCommand(trimmedInput)) {
      this.updatePrompt();
      this.rl.prompt();
      return;
    }

    // Process as AI query
    await this.processAIQuery(trimmedInput);
    this.updatePrompt();
    this.rl.prompt();
  }

  private async handleCommand(input: string): Promise<boolean> {
    const [command, ...args] = input.split(' ');

    switch (command.toLowerCase()) {
      case 'help':
        this.displayHelp();
        return true;

      case 'status':
        this.displayStatus();
        return true;

      case 'dashboard':
        this.toggleDisplayMode('dashboard');
        return true;

      case 'compact':
        this.toggleDisplayMode('compact');
        return true;

      case 'minimal':
        this.toggleDisplayMode('minimal');
        return true;

      case 'theme':
        if (args.length > 0) {
          this.setTheme(args[0]);
        } else {
          this.showAvailableThemes();
        }
        return true;

      case 'models':
        await this.displayModels();
        return true;

      case 'model':
        if (args.length > 0) {
          await this.switchModel(args[0]);
        } else {
          console.log(chalk.yellow('Please specify a model name. Use "models" to see available options.'));
        }
        return true;

      case 'context':
        await this.handleContextCommand(args);
        return true;

      case 'scan':
        await this.scanForErrors();
        return true;

      case 'diagnostics':
      case 'diag':
        await this.runConnectionDiagnostics();
        return true;

      case 'reinit':
        if (args.length > 0) {
          await this.reinitializeProvider(args[0]);
        } else {
          console.log(chalk.yellow('Please specify a provider to reinitialize: openai, anthropic, deepseek, or ollama'));
        }
        return true;

      case 'clear':
        this.clearSession();
        return true;

      case 'refresh':
        this.refreshDisplay();
        return true;

      case 'diff':
        await this.showDiffReview();
        return true;

      case 'review':
        await this.reviewChanges();
        return true;

      case 'exit':
      case 'quit':
        this.handleExit();
        return true;

      default:
        return false;
    }
  }

  private updatePrompt(): void {
    const newPrompt = this.createDynamicPrompt();
    this.rl.setPrompt(newPrompt);
  }

  private toggleDisplayMode(mode: 'dashboard' | 'compact' | 'minimal'): void {
    this.displayMode = mode;
    console.log(chalk.green(`✅ Display mode set to: ${mode}`));
    this.refreshDisplay();
  }

  private setTheme(themeName: string): void {
    try {
      this.modernUI.setTheme(themeName);
      console.log(chalk.green(`✅ Theme set to: ${themeName}`));
      this.refreshDisplay();
    } catch (error) {
      console.log(chalk.red(`❌ Invalid theme: ${themeName}`));
      this.showAvailableThemes();
    }
  }

  private showAvailableThemes(): void {
    console.log(chalk.cyan('\n🎨 Available Themes:\n'));
    console.log(chalk.white('  default  - Default cyan theme'));
    console.log(chalk.white('  dark     - Dark theme with white text'));
    console.log(chalk.white('  matrix   - Green matrix-style theme'));
    console.log(chalk.white('  ocean    - Blue ocean theme'));
    console.log(chalk.gray('\nUsage: theme <name>'));
    console.log();
  }

  private displayHelp(): void {
    const helpContent = `
${chalk.cyan.bold('📖 KRITRIMA AI - COMMAND REFERENCE')}

${chalk.yellow('🔧 Core Commands:')}
  help                 - Show this help message
  status               - Show current status and context
  models               - List available AI models
  model <name>         - Switch to a different model
  scan                 - Scan for errors in working directory
  diagnostics/diag     - Run connection and provider diagnostics
  reinit <provider>    - Reinitialize a specific provider (openai, anthropic, deepseek, ollama)
  clear                - Clear current session
  refresh              - Refresh display
  diff                 - Show diff review sandboxes
  review               - Review recent changes
  exit/quit            - Exit the application

${chalk.yellow('📁 Context Management:')}
  context add <path>   - Add file/directory to context
  context remove <path> - Remove file from context
  context list         - List files in context

${chalk.yellow('🎨 Interface & Themes:')}
  dashboard            - Switch to full dashboard view
  compact              - Switch to compact header view
  minimal              - Switch to minimal view
  theme                - Show available themes
  theme <name>         - Set theme (default, dark, matrix, ocean)

${chalk.yellow('🛠️ Advanced Tools (AI Function Calling):')}
  • File Operations    - Read, write, edit, copy, move files
  • Shell Commands     - Execute system commands safely
  • Code Analysis      - Analyze code quality, complexity, structure
  • Code Refactoring   - Extract functions, rename variables, optimize
  • System Monitoring  - Monitor performance, memory, disk usage
  • AI Assistant       - Code review, documentation, bug detection
  • Web Operations     - Fetch URLs, search, download content
  • Grep & Search      - Advanced text search and pattern matching
  • Diagnostics        - System health checks and troubleshooting

${chalk.yellow('� Special Features:')}
  • Dynamic Prompt     - Visual status indicators in prompt
  • Double ESC         - Interrupt ongoing operations
  • Real-time Context  - Automatic file monitoring
  • Streaming Responses- Real-time AI responses
  • Error Auto-fix     - Automatic error detection & fixing
  • Function Calling   - Autonomous tool execution
  • Modern UI          - Enhanced visual interface
  • Code Generation    - AI-powered code creation and optimization
  • Performance Analytics - Real-time system monitoring
  • Advanced Refactoring - Intelligent code improvements
  • Diff Review        - Change analysis and approval workflow
`;

    const helpBox = this.modernUI.createBox(helpContent.trim(), 'Help & Commands', 'rounded');
    console.log(helpBox);
    console.log();
  }

  private async processAIQuery(query: string): Promise<void> {
    if (!this.currentSession) {
      console.log(chalk.red('❌ No active session. Please restart the application.'));
      return;
    }

    // Check if AI models are available
    if (this.currentSession.model.name === 'none') {
      console.log(chalk.yellow('⚠️  No AI model configured.'));
      console.log(chalk.gray('   Please configure a model first using:'));
      console.log(chalk.gray('   1. Run "models" to see available models'));
      console.log(chalk.gray('   2. Run "model <name>" to select a model'));
      return;
    }

    try {
      this.isProcessing = true;
      this.activeOperations.push('Processing AI query');

      const modernSpinner = this.modernUI.createSpinner('Processing query...');
      modernSpinner.start();

      // Add user message to session
      const userMessage: Message = {
        role: 'user',
        content: query
      };
      this.currentSession.messages.push(userMessage);

      // Get AI response with function calling
      const response = await this.modelManager.generateResponse(
        this.currentSession.messages,
        this.currentSession.context,
        this.toolManager.getAvailableTools(),
        this.currentSession.config
      );

      modernSpinner.succeed('Query processed successfully');

      // Handle streaming response or tool calls
      if (response.toolCalls && response.toolCalls.length > 0) {
        // Add assistant message with tool calls BEFORE executing tools
        const assistantMessage: Message = {
          role: 'assistant',
          content: response.content,
          tool_calls: response.toolCalls
        };
        this.currentSession.messages.push(assistantMessage);

        await this.handleToolCalls(response.toolCalls);
      } else if (response.stream) {
        await this.handleStreamingResponse(response);

        // Add assistant message to session for streaming
        const assistantMessage: Message = {
          role: 'assistant',
          content: response.content
        };
        this.currentSession.messages.push(assistantMessage);
      } else {
        console.log(chalk.blue('\n🤖 ') + response.content);

        // Add assistant message to session for regular response
        const assistantMessage: Message = {
          role: 'assistant',
          content: response.content
        };
        this.currentSession.messages.push(assistantMessage);
      }

      this.currentSession.updatedAt = new Date();

    } catch (error) {
      await this.handleErrorWithResilience(error, 'AI query processing');
    } finally {
      this.isProcessing = false;
      this.activeOperations = this.activeOperations.filter(op => op !== 'Processing AI query');
    }
  }

  private async handleToolCalls(toolCalls: any[]): Promise<void> {
    const toolsHeader = this.modernUI.createBox(
      `🔧 Executing ${toolCalls.length} tool${toolCalls.length > 1 ? 's' : ''}...`,
      'Autonomous Tool Execution',
      'rounded'
    );
    console.log(toolsHeader);
    console.log();

    const toolResults: any[] = [];

    for (let i = 0; i < toolCalls.length; i++) {
      const toolCall = toolCalls[i];
      try {
        const tool = this.toolManager.getTool(toolCall.function.name);
        if (!tool) {
          console.log(chalk.red(`❌ Unknown tool: ${toolCall.function.name}`));
          const errorResult = {
            success: false,
            output: '',
            error: `Unknown tool: ${toolCall.function.name}`
          };
          toolResults.push({ toolCall, result: errorResult });
          continue;
        }

        const operationName = `Executing ${toolCall.function.name}`;
        this.activeOperations.push(operationName);

        const toolSpinner = this.modernUI.createSpinner(`[${i + 1}/${toolCalls.length}] Running ${toolCall.function.name}...`);
        toolSpinner.start();

        const args = JSON.parse(toolCall.function.arguments);

        // Validate tool arguments
        const validation = this.toolManager.validateToolArgs(toolCall.function.name, args);
        if (!validation.valid) {
          const errorResult = {
            success: false,
            output: '',
            error: `Invalid arguments: ${validation.errors.join(', ')}`
          };
          toolResults.push({ toolCall, result: errorResult });
          toolSpinner.fail(`[${i + 1}/${toolCalls.length}] ${toolCall.function.name} failed: Invalid arguments`);
          continue;
        }

        const result = await this.toolManager.executeToolWithContext(
          toolCall.function.name,
          args,
          this.currentSession?.context
        );

        if (result.success) {
          toolSpinner.succeed(`[${i + 1}/${toolCalls.length}] ${toolCall.function.name} completed successfully`);

          // Display tool result summary if it has meaningful output
          if (result.output && result.output.trim()) {
            this.displayToolResultSummary(toolCall.function.name, result);
          }
        } else {
          toolSpinner.fail(`[${i + 1}/${toolCalls.length}] ${toolCall.function.name} failed: ${result.error}`);
        }

        // Store tool result for processing
        toolResults.push({ toolCall, result });

        // Add tool result to session with proper tool_call_id
        const toolMessage: Message = {
          role: 'function',
          name: toolCall.id, // Use tool call ID instead of function name
          content: JSON.stringify(result)
        };
        this.currentSession?.messages.push(toolMessage);

        this.activeOperations = this.activeOperations.filter(op => op !== operationName);

      } catch (error) {
        console.log(chalk.red(`❌ Error executing ${toolCall.function.name}: ${error}`));
        const errorResult = {
          success: false,
          output: '',
          error: error instanceof Error ? error.message : 'Unknown error'
        };
        toolResults.push({ toolCall, result: errorResult });

        // Add error result to session with proper tool_call_id
        const toolMessage: Message = {
          role: 'function',
          name: toolCall.id, // Use tool call ID instead of function name
          content: JSON.stringify(errorResult)
        };
        this.currentSession?.messages.push(toolMessage);

        this.activeOperations = this.activeOperations.filter(op => op !== `Executing ${toolCall.function.name}`);
      }
    }

    console.log(chalk.green('\n✅ Tool execution completed\n'));

    // Generate follow-up response from AI to interpret tool results
    await this.generateFollowUpResponse(toolResults);
  }

  private async generateFollowUpResponse(toolResults: any[]): Promise<void> {
    try {
      // Check if we have a valid session
      if (!this.currentSession) {
        console.log(chalk.red('❌ No active session for follow-up response'));
        return;
      }

      // Check if we have any meaningful tool results to process
      const hasResults = toolResults.some(tr => tr.result.success && tr.result.output);

      if (!hasResults) {
        console.log(chalk.yellow('🤖 No meaningful results to process from tool execution.'));
        return;
      }

      const followUpSpinner = this.modernUI.createSpinner('Generating AI response to tool results...');
      followUpSpinner.start();

      // Generate response from AI to interpret tool results
      const response = await this.modelManager.generateResponse(
        this.currentSession.messages,
        this.currentSession.context,
        this.toolManager.getAvailableTools(),
        this.currentSession.config
      );

      followUpSpinner.succeed('AI response generated');

      // Handle the response (could be more tool calls or final response)
      if (response.toolCalls && response.toolCalls.length > 0) {
        // Add assistant message with tool calls
        const assistantMessage: Message = {
          role: 'assistant',
          content: response.content,
          tool_calls: response.toolCalls
        };
        this.currentSession.messages.push(assistantMessage);

        console.log(chalk.blue('\n🤖 ') + (response.content || 'Executing additional tools...'));

        // Handle additional tool calls
        await this.handleToolCalls(response.toolCalls);
      } else if (response.stream) {
        // Handle streaming response
        await this.handleStreamingResponse(response);

        // Add assistant message to session
        const assistantMessage: Message = {
          role: 'assistant',
          content: response.content
        };
        this.currentSession.messages.push(assistantMessage);
      } else {
        // Display final response
        console.log(chalk.blue('\n🤖 ') + response.content);

        // Add assistant message to session
        const assistantMessage: Message = {
          role: 'assistant',
          content: response.content
        };
        this.currentSession.messages.push(assistantMessage);
      }

      this.currentSession.updatedAt = new Date();

    } catch (error) {
      console.log(chalk.red(`❌ Error generating follow-up response: ${error}`));

      // Enhanced error handling with recovery suggestions
      if (error instanceof Error) {
        if (error.message.includes('tool_calls')) {
          console.log(chalk.yellow('💡 This appears to be a tool call sequencing issue. Attempting recovery...'));

          // Try to clean up message history and retry once
          if (this.currentSession && this.currentSession.messages.length > 0) {
            // Remove any orphaned tool result messages
            const cleanedMessages = this.cleanupMessageHistory(this.currentSession.messages);
            this.currentSession.messages = cleanedMessages;

            console.log(chalk.blue('🔄 Message history cleaned. You can try your request again.'));
          }
        } else if (error.message.includes('API') || error.message.includes('Connection')) {
          console.log(chalk.yellow('💡 API/Connection error detected. Running diagnostics...'));
          await this.runConnectionDiagnostics();
        }
      }
    }
  }

  private displayToolResultSummary(toolName: string, result: any): void {
    const maxOutputLength = 200; // Limit output display length
    let output = result.output || '';

    if (output.length > maxOutputLength) {
      output = output.substring(0, maxOutputLength) + '...';
    }

    // Format output based on tool type
    switch (toolName) {
      case 'grep':
        if (result.metadata?.totalMatches) {
          console.log(chalk.gray(`   📋 Found ${result.metadata.totalMatches} matches in ${result.metadata.matchingFiles} files`));
          if (result.metadata.totalMatches > 0 && output.trim()) {
            const firstMatch = output.split('\n')[0];
            console.log(chalk.gray(`   🔍 First match: ${firstMatch.substring(0, 100)}...`));
          }
        }
        break;
      case 'file':
        if (result.metadata?.action === 'read') {
          const lineCount = output.split('\n').length;
          console.log(chalk.gray(`   📄 Read ${lineCount} lines from ${result.metadata?.path || 'file'}`));
        } else if (result.metadata?.action === 'write') {
          console.log(chalk.gray(`   ✍️  Written to ${result.metadata?.path || 'file'}`));
        } else if (result.metadata?.action === 'list') {
          const items = output.split('\n').filter((line: string) => line.trim()).length;
          console.log(chalk.gray(`   📁 Listed ${items} items`));
        }
        break;
      case 'shell':
        if (result.metadata?.exitCode === 0) {
          console.log(chalk.gray(`   🔧 Command executed successfully (${result.metadata?.executionTime || 0}ms)`));
          if (output.trim()) {
            const firstLine = output.split('\n')[0];
            console.log(chalk.gray(`   💬 Output: ${firstLine.substring(0, 80)}...`));
          }
        }
        break;
      case 'edit':
        if (result.metadata?.linesChanged) {
          console.log(chalk.gray(`   ✏️  Modified ${result.metadata.linesChanged} lines`));
        }
        break;
      case 'write':
        if (result.metadata?.bytesWritten) {
          console.log(chalk.gray(`   💾 Wrote ${result.metadata.bytesWritten} bytes`));
        }
        break;
      case 'web':
        if (result.metadata?.url) {
          console.log(chalk.gray(`   🌐 Fetched content from ${result.metadata.url}`));
        }
        break;
      default:
        if (output.trim()) {
          const firstLine = output.split('\n')[0];
          console.log(chalk.gray(`   📤 ${firstLine.substring(0, 100)}${firstLine.length > 100 ? '...' : ''}`));
        }
    }

    // Show execution time if available
    if (result.metadata?.executionTime) {
      console.log(chalk.gray(`   ⏱️  Execution time: ${result.metadata.executionTime}ms`));
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private handleExit(): void {
    console.log(chalk.cyan('\n👋 Goodbye! Thanks for using Kritrima AI.\n'));
    process.exit(0);
  }

  private async displayModels(): Promise<void> {
    const models = await this.modelManager.getAvailableModels();
    console.log(chalk.cyan('\n🤖 Available Models:\n'));

    for (const model of models) {
      const current = model.name === this.currentSession?.model.name ? chalk.green('(current)') : '';
      console.log(chalk.white(`  ${model.name} - ${model.provider} ${current}`));
    }
    console.log();
  }

  private async switchModel(modelName: string): Promise<void> {
    try {
      const model = await this.modelManager.getModel(modelName);
      if (this.currentSession) {
        this.currentSession.model = model;
        this.currentSession.config = await this.modelManager.getConfigForModel(modelName);
        console.log(chalk.green(`✅ Switched to model: ${modelName}`));

        // Refresh the header to show new model
        this.refreshDisplay();
      }
    } catch (error) {
      console.log(chalk.red(`❌ Failed to switch model: ${error}`));
    }
  }

  private async handleContextCommand(args: string[]): Promise<void> {
    if (args.length === 0) {
      console.log(chalk.yellow('Please specify a context action: add, remove, or list'));
      return;
    }

    const [action, path] = args;

    switch (action.toLowerCase()) {
      case 'add':
        if (path) {
          await this.addToContext(path);
        } else {
          console.log(chalk.yellow('Please specify a file or directory path to add'));
        }
        break;

      case 'remove':
        if (path) {
          this.removeFromContext(path);
        } else {
          console.log(chalk.yellow('Please specify a file path to remove'));
        }
        break;

      case 'list':
        this.listContext();
        break;

      default:
        console.log(chalk.yellow('Unknown context action. Use: add, remove, or list'));
    }
  }

  private async addToContext(path: string): Promise<void> {
    try {
      await this.contextManager.addFile(path);
      if (this.currentSession) {
        this.currentSession.context = this.contextManager.getContext();
      }
      console.log(chalk.green(`✅ Added ${path} to context`));
    } catch (error) {
      console.log(chalk.red(`❌ Failed to add ${path} to context: ${error}`));
    }
  }

  private removeFromContext(path: string): void {
    this.contextManager.removeFile(path);
    if (this.currentSession) {
      this.currentSession.context = this.contextManager.getContext();
    }
    console.log(chalk.green(`✅ Removed ${path} from context`));
  }

  private listContext(): void {
    const context = this.contextManager.getContext();
    console.log(chalk.cyan('\n📁 Context Files:\n'));

    if (context.length === 0) {
      console.log(chalk.gray('  No files in context'));
    } else {
      for (const file of context) {
        console.log(chalk.white(`  ${file.path} (${file.size} bytes)`));
      }
    }
    console.log();
  }

  private async scanForErrors(): Promise<void> {
    this.spinner.start('Scanning for errors...');

    try {
      const errors = await this.errorDetector.scanDirectory(this.config.workingDirectory);
      this.spinner.stop();

      if (errors.length === 0) {
        console.log(chalk.green('✅ No errors found!'));
      } else {
        console.log(chalk.yellow(`⚠️  Found ${errors.length} errors:`));
        for (const error of errors) {
          const severity = error.severity === 'error' ? chalk.red('ERROR') :
                          error.severity === 'warning' ? chalk.yellow('WARN') :
                          chalk.blue('INFO');
          console.log(`  ${severity}: ${error.file}:${error.line} - ${error.message}`);
        }

        if (this.config.autoFix) {
          console.log(chalk.blue('\n🔧 Attempting to auto-fix errors...'));
          await this.autoFixErrors(errors);
        }
      }
    } catch (error) {
      this.spinner.stop();
      console.log(chalk.red(`❌ Error scanning directory: ${error}`));
    }
  }

  private async autoFixErrors(errors: any[]): Promise<void> {
    const fixableErrors = errors.filter(e => e.fixable);

    if (fixableErrors.length === 0) {
      console.log(chalk.yellow('No auto-fixable errors found'));
      return;
    }

    for (const error of fixableErrors) {
      try {
        await this.errorDetector.fixError(error);
        console.log(chalk.green(`✅ Fixed: ${error.file}:${error.line}`));
      } catch (fixError) {
        console.log(chalk.red(`❌ Failed to fix: ${error.file}:${error.line} - ${fixError}`));
      }
    }
  }

  private async reinitializeProvider(providerName: string): Promise<void> {
    console.log(chalk.blue(`🔄 Reinitializing ${providerName} provider...`));

    try {
      const success = await this.modelManager.reinitializeProvider(providerName);

      if (success) {
        console.log(chalk.green(`✅ ${providerName} provider reinitialized successfully`));

        // Test the reinitialized provider
        const testResult = await this.modelManager.testAllProviders();
        if (testResult[providerName]) {
          console.log(chalk.green(`✅ ${providerName} provider is now working`));

          // If current session is using a failed provider, suggest switching
          if (this.currentSession && this.currentSession.model.provider !== providerName) {
            const currentProviderWorking = testResult[this.currentSession.model.provider];
            if (!currentProviderWorking) {
              console.log(chalk.yellow(`💡 Consider switching to ${providerName} with: model ${providerName}-chat`));
            }
          }
        } else {
          console.log(chalk.yellow(`⚠️  ${providerName} provider reinitialized but connection test failed`));
        }
      } else {
        console.log(chalk.red(`❌ Failed to reinitialize ${providerName} provider`));
        console.log(chalk.gray('   Check your API keys and configuration'));
      }
    } catch (error) {
      console.log(chalk.red(`❌ Error reinitializing ${providerName}: ${error}`));
    }
  }

  private clearSession(): void {
    if (this.currentSession) {
      this.currentSession.messages = [];
      console.log(chalk.green('✅ Session cleared'));
    }
  }

  private handleStepStart(step: any): void {
    console.log(chalk.blue(`🔄 Starting step: ${step.name}`));
  }

  private handleStepComplete(step: any): void {
    if (step.result?.success) {
      console.log(chalk.green(`✅ Step completed: ${step.name}`));
    } else {
      console.log(chalk.red(`❌ Step failed: ${step.name} - ${step.result?.error}`));
    }
  }

  private handlePlanComplete(plan: ExecutionPlan): void {
    const completed = plan.steps.filter(s => s.status === 'completed').length;
    const total = plan.steps.length;
    console.log(chalk.cyan(`📋 Plan completed: ${completed}/${total} steps successful`));
  }

  private handleErrorDetected(error: any): void {
    console.log(chalk.red(`🚨 Error detected: ${error.message}`));
  }



  private async runConnectionDiagnostics(): Promise<void> {
    console.log(chalk.blue('\n🔍 Running comprehensive diagnostics...\n'));

    try {
      // Get enhanced provider diagnostics
      const diagnostics = await this.modelManager.getProviderDiagnostics();

      console.log(chalk.cyan('📡 Provider Status:'));
      let workingCount = 0;

      for (const [provider, info] of Object.entries(diagnostics)) {
        let statusIcon = '';
        let statusText = '';

        switch (info.status) {
          case 'working':
            statusIcon = chalk.green('✅');
            statusText = chalk.green('Working');
            workingCount++;
            break;
          case 'failed':
            statusIcon = chalk.red('❌');
            statusText = chalk.red('Failed');
            break;
          case 'error':
            statusIcon = chalk.red('💥');
            statusText = chalk.red('Error');
            break;
          default:
            statusIcon = chalk.yellow('⚠️');
            statusText = chalk.yellow('Unknown');
        }

        console.log(`  ${provider}: ${statusIcon} ${statusText}`);

        // Show additional details
        if (info.models && info.models.length > 0) {
          console.log(chalk.gray(`    Models: ${info.models.slice(0, 3).join(', ')}${info.models.length > 3 ? '...' : ''}`));
        }

        if (info.error) {
          console.log(chalk.red(`    Error: ${info.error}`));
        }

        // Provider-specific diagnostics
        if (provider === 'deepseek' && info.connectionHealth) {
          const health = info.connectionHealth;
          const healthStatus = health.healthy ? chalk.green('Healthy') : chalk.red('Unhealthy');
          console.log(chalk.gray(`    Connection: ${healthStatus}`));
          if (health.lastCheck) {
            console.log(chalk.gray(`    Last Check: ${new Date(health.lastCheck).toLocaleTimeString()}`));
          }
        }

        if (provider === 'ollama' && info.serverHealthy !== undefined) {
          const serverStatus = info.serverHealthy ? chalk.green('Running') : chalk.red('Not Running');
          console.log(chalk.gray(`    Server: ${serverStatus}`));
          if (info.serverInfo) {
            console.log(chalk.gray(`    Version: ${info.serverInfo.version || 'Unknown'}`));
          }
        }
      }

      // Summary
      if (workingCount > 0) {
        console.log(chalk.green(`\n✅ ${workingCount} provider(s) working`));

        // Get best provider recommendation
        const bestProvider = await this.modelManager.getBestProvider();
        if (bestProvider && this.currentSession) {
          const currentProvider = this.currentSession.model.provider;
          if (currentProvider !== bestProvider.name) {
            console.log(chalk.yellow(`💡 Recommended provider: ${bestProvider.name}`));
            console.log(chalk.gray(`   Use: model ${bestProvider.model}`));
          }
        }
      } else {
        console.log(chalk.red('\n❌ No providers are currently working'));
        console.log(chalk.yellow('\n🔧 Troubleshooting steps:'));
        console.log(chalk.gray('  1. Check your internet connection'));
        console.log(chalk.gray('  2. Verify API keys in .env file'));
        console.log(chalk.gray('  3. Check if Ollama is running (for local models)'));
        console.log(chalk.gray('  4. Try "reinit <provider>" to reinitialize a provider'));
        console.log(chalk.gray('  5. Try again in a few minutes'));
      }

      // Check specific issues
      await this.checkSpecificIssues();

    } catch (error) {
      console.log(chalk.red(`❌ Diagnostics failed: ${error}`));
    }

    console.log(); // Add spacing
  }

  private async checkSpecificIssues(): Promise<void> {
    console.log(chalk.cyan('\n🔍 Checking specific issues:'));

    // Check internet connectivity
    try {
      const https = require('https');
      await new Promise((resolve, reject) => {
        const req = https.get('https://www.google.com', { timeout: 5000 }, resolve);
        req.on('error', reject);
        req.on('timeout', () => {
          req.destroy();
          reject(new Error('timeout'));
        });
      });
      console.log(chalk.green('  ✅ Internet connectivity: OK'));
    } catch (error) {
      console.log(chalk.red('  ❌ Internet connectivity: Failed'));
      console.log(chalk.yellow('    💡 Check your network connection'));
    }

    // Check DNS resolution for Deepseek
    try {
      const dns = require('dns');
      await new Promise((resolve, reject) => {
        dns.lookup('api.deepseek.com', (err: any) => {
          if (err) reject(err);
          else resolve(true);
        });
      });
      console.log(chalk.green('  ✅ Deepseek DNS resolution: OK'));
    } catch (error) {
      console.log(chalk.red('  ❌ Deepseek DNS resolution: Failed'));
      console.log(chalk.yellow('    💡 DNS or firewall issue detected'));
    }

    // Check environment variables
    const envVars = ['DEEPSEEK_API_KEY', 'OPENAI_API_KEY', 'ANTHROPIC_API_KEY'];
    let hasValidKey = false;

    for (const envVar of envVars) {
      const value = process.env[envVar];
      if (value && value !== 'your_api_key_here' && value.length > 10) {
        console.log(chalk.green(`  ✅ ${envVar}: Configured`));
        hasValidKey = true;
      } else {
        console.log(chalk.yellow(`  ⚠️  ${envVar}: Not configured`));
      }
    }

    if (!hasValidKey) {
      console.log(chalk.red('\n  ❌ No valid API keys found'));
      console.log(chalk.yellow('    💡 Configure at least one API key in .env file'));
    }
  }

  // Enhanced UI/UX Methods
  private displayHeader(): void {
    if (!this.currentSession) {
      return;
    }

    const sessionId = this.currentSession.id.split('_')[1] || 'initializing';
    const modelName = this.currentSession.model.name || 'loading';
    const provider = this.currentSession.model.provider || 'unknown';
    const workingDir = path.basename(this.config.workingDirectory);

    // Clear previous header and display new one
    console.log(chalk.bgBlue.white.bold(' KRITRIMA AI ') +
                chalk.bgGray.white(` Session: ${sessionId} `) +
                chalk.bgGreen.white(` Model: ${modelName} (${provider}) `) +
                chalk.bgYellow.black(` Directory: ${workingDir} `));
    console.log();
  }

  private refreshDisplay(): void {
    console.log(); // Add some space
    this.displayInterface();
  }

  private handleKeyPress(_str: string, key: any): void {
    if (key && key.name === 'escape') {
      this.escapeCount++;

      if (this.escapeTimer) {
        clearTimeout(this.escapeTimer);
      }

      this.escapeTimer = setTimeout(() => {
        this.escapeCount = 0;
      }, 500); // Reset after 500ms

      if (this.escapeCount === 2) {
        this.handleDoubleEscape();
        this.escapeCount = 0;
      }
    }
  }

  private handleDoubleEscape(): void {
    if (this.isProcessing || this.streamingResponse) {
      console.log(chalk.yellow('\n⚠️  Interrupting current operation...'));
      this.isProcessing = false;
      this.streamingResponse = false;

      if (this.spinner.isSpinning) {
        this.spinner.stop();
      }

      console.log(chalk.cyan('✅ Operation interrupted. You can continue with a new query.'));
      this.rl.prompt();
    } else {
      console.log(chalk.blue('\n💡 Double ESC detected. Use this to interrupt ongoing operations.'));
      this.rl.prompt();
    }
  }

  // Enhanced streaming response handling
  private async handleStreamingResponse(response: any): Promise<void> {
    this.streamingResponse = true;

    try {
      if (response.stream) {
        process.stdout.write(chalk.blue('\n🤖 '));

        for await (const chunk of response.stream) {
          if (!this.streamingResponse) {
            break; // Interrupted by double ESC
          }

          if (chunk.content) {
            process.stdout.write(chunk.content);
          }
        }

        console.log(); // New line after streaming
      } else {
        console.log(chalk.blue('\n🤖 ') + response.content);
      }
    } catch (error) {
      console.log(chalk.red(`\n❌ Streaming error: ${error}`));
    } finally {
      this.streamingResponse = false;
    }
  }

  // Enhanced error handling with resilience
  private async handleErrorWithResilience(error: any, context: string): Promise<void> {
    console.log(chalk.red(`❌ Error in ${context}: ${error.message || 'Unknown error'}`));

    // Log detailed error for debugging
    if (this.config.logLevel === 'debug') {
      console.log(chalk.gray('Debug info:'), error.stack);
    }

    // Attempt recovery based on error type
    if (error.message?.includes('API key') || error.message?.includes('unauthorized')) {
      console.log(chalk.yellow('💡 Tip: Check your API keys in the .env file'));
      console.log(chalk.gray('   Run "diagnostics" to check API key configuration'));
    } else if (error.message?.includes('network') || error.message?.includes('timeout') || error.message?.includes('Connection error')) {
      console.log(chalk.yellow('💡 Tip: Check your internet connection and try again'));
      console.log(chalk.gray('   Run "diagnostics" to check network connectivity'));

      // Try to switch to a working provider
      await this.attemptProviderFallback();
    } else if (error.message?.includes('model') || error.message?.includes('provider')) {
      console.log(chalk.yellow('💡 Tip: Try switching to a different model with "model <name>"'));
      console.log(chalk.gray('   Run "models" to see available models'));

      // Try to switch to a working provider
      await this.attemptProviderFallback();
    } else if (error.message?.includes('tool_calls') || error.message?.includes('function')) {
      console.log(chalk.yellow('💡 Tool execution issue detected. Cleaning up session...'));
      this.cleanupSession();
    } else if (error.message?.includes('rate limit') || error.message?.includes('quota')) {
      console.log(chalk.yellow('💡 Rate limit exceeded. Try again in a few minutes or switch providers'));
      await this.attemptProviderFallback();
    }

    // Graceful degradation
    console.log(chalk.cyan('🔄 Kritrima AI is still running. You can continue with other operations.'));
  }

  // Attempt to fallback to a working provider
  private async attemptProviderFallback(): Promise<void> {
    if (!this.currentSession) return;

    try {
      const bestProvider = await this.modelManager.getBestProvider();
      if (bestProvider && bestProvider.name !== this.currentSession.model.provider) {
        console.log(chalk.blue(`🔄 Attempting to switch to working provider: ${bestProvider.name}`));

        const newModel = await this.modelManager.getModel(bestProvider.model);
        this.currentSession.model = newModel;

        this.currentSession.config = await this.modelManager.getConfigForModel(bestProvider.model);
        console.log(chalk.green(`✅ Switched to ${bestProvider.name} (${bestProvider.model})`));
      }
    } catch (fallbackError) {
      console.log(chalk.gray('   Unable to switch providers automatically'));
    }
  }

  // Clean up session state
  private cleanupSession(): void {
    if (!this.currentSession) return;

    // Clean up message history
    this.currentSession.messages = this.cleanupMessageHistory(this.currentSession.messages);
    console.log(chalk.blue('🧹 Session cleaned up. You can try your request again.'));
  }

  // Clean up message history to remove orphaned tool calls
  private cleanupMessageHistory(messages: Message[]): Message[] {
    const cleanedMessages: Message[] = [];
    let pendingToolCalls: any[] = [];

    for (const message of messages) {
      if (message.role === 'assistant' && message.tool_calls) {
        // Track pending tool calls
        pendingToolCalls = message.tool_calls;
        cleanedMessages.push(message);
      } else if (message.role === 'function') {
        // Only keep function messages that have corresponding tool calls
        const hasCorrespondingCall = pendingToolCalls.some(tc => tc.id === message.name);
        if (hasCorrespondingCall) {
          cleanedMessages.push(message);
          // Remove the processed tool call
          pendingToolCalls = pendingToolCalls.filter(tc => tc.id !== message.name);
        }
      } else {
        // Keep all other messages
        cleanedMessages.push(message);
        // Clear pending tool calls for non-assistant messages
        if (message.role !== 'assistant') {
          pendingToolCalls = [];
        }
      }
    }

    return cleanedMessages;
  }

  // Enhanced context loading with progress indication
  private async loadContextWithProgress(directory: string): Promise<void> {
    const spinner = ora('Loading codebase context...').start();

    try {
      await this.contextManager.loadWorkingDirectory(directory);
      const contextFiles = this.contextManager.getContext();
      const totalTokens = this.contextManager.getTotalTokens();

      spinner.succeed(`Loaded ${contextFiles.length} files (${totalTokens.toLocaleString()} tokens)`);

      if (this.currentSession) {
        this.currentSession.context = contextFiles;
      }

    } catch (error) {
      spinner.fail(`Failed to load context: ${error}`);
      throw error;
    }
  }

  // Real-time codebase monitoring
  private startCodebaseMonitoring(): void {
    const chokidar = require('chokidar');

    const watcher = chokidar.watch(this.config.workingDirectory, {
      ignored: /(^|[\/\\])\../, // ignore dotfiles
      persistent: true,
      ignoreInitial: true
    });

    watcher.on('change', async (filePath: string) => {
      if (this.shouldMonitorFile(filePath)) {
        console.log(chalk.gray(`📝 File changed: ${path.basename(filePath)}`));

        try {
          await this.contextManager.addFile(filePath);
          if (this.currentSession) {
            this.currentSession.context = this.contextManager.getContext();
          }
        } catch (error) {
          // Silent fail for file monitoring
        }
      }
    });
  }

  private shouldMonitorFile(filePath: string): boolean {
    const ext = path.extname(filePath);
    const monitoredExtensions = ['.ts', '.js', '.py', '.java', '.cpp', '.c', '.go', '.rs', '.php'];
    return monitoredExtensions.includes(ext);
  }

  // Enhanced theme support
  private applyTheme(themeName: string = 'default'): void {
    const themes = {
      default: {
        primary: chalk.cyan,
        secondary: chalk.blue,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.gray
      },
      dark: {
        primary: chalk.white,
        secondary: chalk.gray,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.dim
      },
      matrix: {
        primary: chalk.green,
        secondary: chalk.greenBright,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.dim.green
      }
    };

    // Apply theme (this would be expanded for full theme support)
    const theme = themes[themeName as keyof typeof themes] || themes.default;
    // Store theme for use throughout the interface
    this.modernUI.setTheme(themeName);
  }

  // Diff Review Methods
  private async showDiffReview(): Promise<void> {
    try {
      const sandboxes = this.diffReviewer.listSandboxes();

      if (sandboxes.length === 0) {
        console.log(chalk.yellow('📋 No diff sandboxes available.'));
        console.log(chalk.gray('   Create changes first to review them.'));
        return;
      }

      console.log(chalk.cyan('\n📋 Available Diff Sandboxes:\n'));

      sandboxes.forEach((sandbox: any, index: number) => {
        const statusIcon = sandbox.status === 'approved' ? '✅' :
                          sandbox.status === 'rejected' ? '❌' : '⏳';
        console.log(`${statusIcon} ${index + 1}. ${sandbox.id} (${sandbox.changes.length} changes) - ${sandbox.status}`);
      });

      console.log(chalk.gray('\nUse "review" command to review specific changes.'));
    } catch (error) {
      console.log(chalk.red(`❌ Error showing diff review: ${error}`));
    }
  }

  private async reviewChanges(): Promise<void> {
    try {
      const sandboxes = this.diffReviewer.listSandboxes();

      if (sandboxes.length === 0) {
        console.log(chalk.yellow('📋 No changes to review.'));
        return;
      }

      // For now, review the most recent sandbox
      const latestSandbox = sandboxes[sandboxes.length - 1];
      const review = this.diffReviewer.reviewChanges(latestSandbox.id);

      console.log(chalk.cyan(`\n🔍 Reviewing Sandbox: ${latestSandbox.id}\n`));

      // Display change analysis
      const analysis = review.analysis;
      console.log(chalk.white(`📊 Analysis:`));
      console.log(chalk.gray(`   Total Changes: ${analysis.totalChanges}`));
      console.log(chalk.gray(`   Added Files: ${analysis.addedFiles}`));
      console.log(chalk.gray(`   Modified Files: ${analysis.modifiedFiles}`));
      console.log(chalk.gray(`   Removed Files: ${analysis.removedFiles}`));
      console.log(chalk.gray(`   Lines Added: ${analysis.linesAdded}`));
      console.log(chalk.gray(`   Lines Removed: ${analysis.linesRemoved}`));
      console.log(chalk.gray(`   Risk Level: ${analysis.riskLevel}`));

      // Display issues if any
      if (analysis.issues.length > 0) {
        console.log(chalk.yellow(`\n⚠️  Issues Found:`));
        analysis.issues.forEach((issue: any) => {
          const severityIcon = issue.severity === 'high' ? '🔴' :
                              issue.severity === 'medium' ? '🟡' : '🟢';
          console.log(`${severityIcon} ${issue.type}: ${issue.message}`);
        });
      }

      // Display recommendations
      if (analysis.recommendations.length > 0) {
        console.log(chalk.blue(`\n💡 Recommendations:`));
        analysis.recommendations.forEach((rec: string) => {
          console.log(`   • ${rec}`);
        });
      }

      // Show basic change summary instead of diff preview
      console.log(chalk.cyan(`\n📝 Changes Summary:`));
      latestSandbox.changes.forEach((change: any, index: number) => {
        const typeIcon = change.type === 'add' ? '➕' :
                        change.type === 'modify' ? '✏️' : '➖';
        console.log(`${typeIcon} ${index + 1}. ${change.type}: ${change.path}`);
      });

    } catch (error) {
      console.log(chalk.red(`❌ Error reviewing changes: ${error}`));
    }
  }
}
