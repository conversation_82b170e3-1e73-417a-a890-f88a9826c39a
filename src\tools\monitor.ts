import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { Tool, ToolResult } from '../types';

export class MonitorTool implements Tool {
  name = 'monitor';
  description = 'Advanced system and application monitoring with performance analytics';
  
  parameters = {
    type: 'object',
    properties: {
      action: {
        type: 'string',
        enum: ['system_info', 'performance', 'memory_usage', 'disk_usage', 'network_info', 'process_monitor', 'file_watcher', 'health_check'],
        description: 'Type of monitoring action to perform'
      },
      target: {
        type: 'string',
        description: 'Target to monitor (file path, process name, etc.)'
      },
      duration: {
        type: 'number',
        description: 'Monitoring duration in seconds'
      },
      interval: {
        type: 'number',
        description: 'Monitoring interval in milliseconds'
      },
      format: {
        type: 'string',
        enum: ['json', 'table', 'summary'],
        description: 'Output format'
      },
      options: {
        type: 'object',
        description: 'Additional monitoring options'
      }
    },
    required: ['action']
  };

  private watchers: Map<string, fs.FSWatcher> = new Map();
  private monitors: Map<string, NodeJS.Timeout> = new Map();

  async execute(args: {
    action: string;
    target?: string;
    duration?: number;
    interval?: number;
    format?: string;
    options?: any;
  }): Promise<ToolResult> {
    try {
      const { action, target, duration = 10, interval = 1000, format = 'summary', options = {} } = args;

      switch (action) {
        case 'system_info':
          return await this.getSystemInfo(format);
        
        case 'performance':
          return await this.getPerformanceMetrics(duration, interval, format);
        
        case 'memory_usage':
          return await this.getMemoryUsage(format);
        
        case 'disk_usage':
          return await this.getDiskUsage(target, format);
        
        case 'network_info':
          return await this.getNetworkInfo(format);
        
        case 'process_monitor':
          return await this.monitorProcess(target, duration, interval, format);
        
        case 'file_watcher':
          return await this.watchFiles(target, options);
        
        case 'health_check':
          return await this.performHealthCheck(target, options);
        
        default:
          throw new Error(`Unknown monitoring action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Monitoring operation failed'
      };
    }
  }

  private async getSystemInfo(format: string): Promise<ToolResult> {
    const systemInfo = {
      platform: os.platform(),
      architecture: os.arch(),
      hostname: os.hostname(),
      release: os.release(),
      version: os.version(),
      uptime: os.uptime(),
      loadAverage: os.loadavg(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpuCount: os.cpus().length,
      cpuModel: os.cpus()[0]?.model || 'Unknown',
      networkInterfaces: Object.keys(os.networkInterfaces()),
      homeDirectory: os.homedir(),
      tempDirectory: os.tmpdir(),
      userInfo: os.userInfo()
    };

    const output = this.formatOutput(systemInfo, format, 'System Information');

    return {
      success: true,
      output,
      metadata: {
        action: 'system_info',
        timestamp: new Date().toISOString(),
        systemInfo
      }
    };
  }

  private async getPerformanceMetrics(duration: number, interval: number, format: string): Promise<ToolResult> {
    const metrics: any[] = [];
    const startTime = Date.now();
    const endTime = startTime + (duration * 1000);

    return new Promise((resolve) => {
      const collectMetrics = () => {
        const now = Date.now();
        if (now >= endTime) {
          const summary = this.calculatePerformanceSummary(metrics);
          const output = this.formatOutput(summary, format, 'Performance Metrics');
          
          resolve({
            success: true,
            output,
            metadata: {
              action: 'performance',
              duration,
              interval,
              samplesCollected: metrics.length,
              summary
            }
          });
          return;
        }

        const metric = {
          timestamp: now,
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          uptime: process.uptime(),
          systemMemory: {
            total: os.totalmem(),
            free: os.freemem(),
            used: os.totalmem() - os.freemem()
          },
          loadAverage: os.loadavg()
        };

        metrics.push(metric);
        setTimeout(collectMetrics, interval);
      };

      collectMetrics();
    });
  }

  private async getMemoryUsage(format: string): Promise<ToolResult> {
    const processMemory = process.memoryUsage();
    const systemMemory = {
      total: os.totalmem(),
      free: os.freemem(),
      used: os.totalmem() - os.freemem()
    };

    const memoryInfo = {
      process: {
        rss: this.formatBytes(processMemory.rss),
        heapTotal: this.formatBytes(processMemory.heapTotal),
        heapUsed: this.formatBytes(processMemory.heapUsed),
        external: this.formatBytes(processMemory.external),
        arrayBuffers: this.formatBytes(processMemory.arrayBuffers || 0)
      },
      system: {
        total: this.formatBytes(systemMemory.total),
        free: this.formatBytes(systemMemory.free),
        used: this.formatBytes(systemMemory.used),
        usagePercentage: ((systemMemory.used / systemMemory.total) * 100).toFixed(2) + '%'
      }
    };

    const output = this.formatOutput(memoryInfo, format, 'Memory Usage');

    return {
      success: true,
      output,
      metadata: {
        action: 'memory_usage',
        timestamp: new Date().toISOString(),
        memoryInfo
      }
    };
  }

  private async getDiskUsage(target?: string, format?: string): Promise<ToolResult> {
    const targetPath = target || process.cwd();
    
    try {
      const stats = fs.statSync(targetPath);
      const diskInfo: any = {
        path: targetPath,
        isDirectory: stats.isDirectory(),
        isFile: stats.isFile(),
        size: this.formatBytes(stats.size),
        created: stats.birthtime,
        modified: stats.mtime,
        accessed: stats.atime
      };

      if (stats.isDirectory()) {
        const dirInfo = await this.analyzeDirectory(targetPath);
        diskInfo.directoryAnalysis = dirInfo;
      }

      const output = this.formatOutput(diskInfo, format || 'summary', 'Disk Usage');

      return {
        success: true,
        output,
        metadata: {
          action: 'disk_usage',
          target: targetPath,
          diskInfo
        }
      };
    } catch (error) {
      throw new Error(`Failed to analyze disk usage for ${targetPath}: ${error}`);
    }
  }

  private async getNetworkInfo(format: string): Promise<ToolResult> {
    const networkInterfaces = os.networkInterfaces();
    const networkInfo: any = {};

    for (const [name, interfaces] of Object.entries(networkInterfaces)) {
      if (interfaces) {
        networkInfo[name] = interfaces.map(iface => ({
          address: iface.address,
          netmask: iface.netmask,
          family: iface.family,
          mac: iface.mac,
          internal: iface.internal,
          cidr: iface.cidr
        }));
      }
    }

    const output = this.formatOutput(networkInfo, format, 'Network Information');

    return {
      success: true,
      output,
      metadata: {
        action: 'network_info',
        timestamp: new Date().toISOString(),
        networkInfo
      }
    };
  }

  private async monitorProcess(target?: string, duration?: number, interval?: number, format?: string): Promise<ToolResult> {
    const processInfo = {
      pid: process.pid,
      ppid: process.ppid,
      platform: process.platform,
      arch: process.arch,
      version: process.version,
      versions: process.versions,
      execPath: process.execPath,
      execArgv: process.execArgv,
      argv: process.argv,
      env: Object.keys(process.env).length,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    };

    const output = this.formatOutput(processInfo, format || 'summary', 'Process Monitor');

    return {
      success: true,
      output,
      metadata: {
        action: 'process_monitor',
        target,
        duration,
        processInfo
      }
    };
  }

  private async watchFiles(target?: string, options?: any): Promise<ToolResult> {
    if (!target) {
      throw new Error('Target path is required for file watching');
    }

    const watchPath = path.resolve(target);
    const watcherId = `watcher_${Date.now()}`;

    if (!fs.existsSync(watchPath)) {
      throw new Error(`Path does not exist: ${watchPath}`);
    }

    const events: any[] = [];
    const watcher = fs.watch(watchPath, { recursive: options?.recursive || false }, (eventType, filename) => {
      const event = {
        timestamp: new Date().toISOString(),
        eventType,
        filename,
        path: watchPath
      };
      events.push(event);
    });

    this.watchers.set(watcherId, watcher);

    // Auto-stop after duration if specified
    if (options?.duration) {
      setTimeout(() => {
        this.stopWatcher(watcherId);
      }, options.duration * 1000);
    }

    return {
      success: true,
      output: `File watcher started for ${watchPath} (ID: ${watcherId})`,
      metadata: {
        action: 'file_watcher',
        watcherId,
        watchPath,
        options
      }
    };
  }

  private async performHealthCheck(target?: string, options?: any): Promise<ToolResult> {
    const healthChecks = {
      system: this.checkSystemHealth(),
      memory: this.checkMemoryHealth(),
      disk: await this.checkDiskHealth(target),
      process: this.checkProcessHealth(),
      timestamp: new Date().toISOString()
    };

    const overallHealth = this.calculateOverallHealth(healthChecks);
    
    const output = this.formatOutput({
      ...healthChecks,
      overallHealth
    }, options?.format || 'summary', 'Health Check');

    return {
      success: true,
      output,
      metadata: {
        action: 'health_check',
        target,
        healthChecks,
        overallHealth
      }
    };
  }

  // Helper methods
  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  private formatOutput(data: any, format: string, title: string): string {
    switch (format) {
      case 'json':
        return JSON.stringify(data, null, 2);
      
      case 'table':
        return this.formatAsTable(data, title);
      
      case 'summary':
      default:
        return this.formatAsSummary(data, title);
    }
  }

  private formatAsTable(data: any, title: string): string {
    let output = `\n=== ${title} ===\n`;
    
    const formatValue = (value: any): string => {
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value);
      }
      return String(value);
    };

    for (const [key, value] of Object.entries(data)) {
      output += `${key.padEnd(20)}: ${formatValue(value)}\n`;
    }
    
    return output;
  }

  private formatAsSummary(data: any, title: string): string {
    let output = `\n📊 ${title}\n`;
    output += '─'.repeat(title.length + 4) + '\n';
    
    const formatValue = (value: any, indent: string = ''): string => {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        let result = '\n';
        for (const [k, v] of Object.entries(value)) {
          result += `${indent}  ${k}: ${formatValue(v, indent + '  ')}\n`;
        }
        return result.slice(0, -1); // Remove last newline
      } else if (Array.isArray(value)) {
        return value.join(', ');
      }
      return String(value);
    };

    for (const [key, value] of Object.entries(data)) {
      output += `${key}: ${formatValue(value)}\n`;
    }
    
    return output;
  }

  private calculatePerformanceSummary(metrics: any[]): any {
    if (metrics.length === 0) return {};

    const summary = {
      samplesCount: metrics.length,
      duration: metrics[metrics.length - 1].timestamp - metrics[0].timestamp,
      memory: {
        avgHeapUsed: 0,
        maxHeapUsed: 0,
        minHeapUsed: Number.MAX_VALUE
      },
      system: {
        avgMemoryUsage: 0,
        maxMemoryUsage: 0,
        avgLoadAverage: [0, 0, 0]
      }
    };

    for (const metric of metrics) {
      const heapUsed = metric.memory.heapUsed;
      summary.memory.avgHeapUsed += heapUsed;
      summary.memory.maxHeapUsed = Math.max(summary.memory.maxHeapUsed, heapUsed);
      summary.memory.minHeapUsed = Math.min(summary.memory.minHeapUsed, heapUsed);

      const memoryUsage = (metric.systemMemory.used / metric.systemMemory.total) * 100;
      summary.system.avgMemoryUsage += memoryUsage;
      summary.system.maxMemoryUsage = Math.max(summary.system.maxMemoryUsage, memoryUsage);

      for (let i = 0; i < 3; i++) {
        summary.system.avgLoadAverage[i] += metric.loadAverage[i];
      }
    }

    summary.memory.avgHeapUsed = Math.round(summary.memory.avgHeapUsed / metrics.length);
    summary.system.avgMemoryUsage = Math.round(summary.system.avgMemoryUsage / metrics.length * 100) / 100;
    
    for (let i = 0; i < 3; i++) {
      summary.system.avgLoadAverage[i] = Math.round(summary.system.avgLoadAverage[i] / metrics.length * 100) / 100;
    }

    return summary;
  }

  private async analyzeDirectory(dirPath: string): Promise<any> {
    const analysis = {
      totalFiles: 0,
      totalDirectories: 0,
      totalSize: 0,
      fileTypes: {} as Record<string, number>,
      largestFiles: [] as Array<{name: string, size: number}>
    };

    const analyzeRecursive = (currentPath: string) => {
      try {
        const items = fs.readdirSync(currentPath);
        
        for (const item of items) {
          const itemPath = path.join(currentPath, item);
          const stats = fs.statSync(itemPath);
          
          if (stats.isDirectory()) {
            analysis.totalDirectories++;
            analyzeRecursive(itemPath);
          } else {
            analysis.totalFiles++;
            analysis.totalSize += stats.size;
            
            const ext = path.extname(item).toLowerCase() || 'no extension';
            analysis.fileTypes[ext] = (analysis.fileTypes[ext] || 0) + 1;
            
            analysis.largestFiles.push({ name: itemPath, size: stats.size });
          }
        }
      } catch (error) {
        // Skip inaccessible directories
      }
    };

    analyzeRecursive(dirPath);
    
    // Sort and limit largest files
    analysis.largestFiles.sort((a, b) => b.size - a.size);
    analysis.largestFiles = analysis.largestFiles.slice(0, 10);

    return analysis;
  }

  private checkSystemHealth(): any {
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const memoryUsage = ((totalMem - freeMem) / totalMem) * 100;
    const loadAvg = os.loadavg();
    const cpuCount = os.cpus().length;

    return {
      status: memoryUsage < 80 && loadAvg[0] < cpuCount * 0.8 ? 'healthy' : 'warning',
      memoryUsage: Math.round(memoryUsage * 100) / 100,
      loadAverage: loadAvg,
      cpuCount
    };
  }

  private checkMemoryHealth(): any {
    const usage = process.memoryUsage();
    const heapUsagePercent = (usage.heapUsed / usage.heapTotal) * 100;

    return {
      status: heapUsagePercent < 80 ? 'healthy' : 'warning',
      heapUsagePercent: Math.round(heapUsagePercent * 100) / 100,
      heapUsed: this.formatBytes(usage.heapUsed),
      heapTotal: this.formatBytes(usage.heapTotal)
    };
  }

  private async checkDiskHealth(target?: string): Promise<any> {
    const checkPath = target || process.cwd();
    
    try {
      const stats = fs.statSync(checkPath);
      return {
        status: 'healthy',
        accessible: true,
        path: checkPath,
        isDirectory: stats.isDirectory()
      };
    } catch (error) {
      return {
        status: 'error',
        accessible: false,
        path: checkPath,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private checkProcessHealth(): any {
    const uptime = process.uptime();
    const memUsage = process.memoryUsage();

    return {
      status: uptime > 0 && memUsage.heapUsed > 0 ? 'healthy' : 'warning',
      uptime: Math.round(uptime),
      pid: process.pid,
      memoryUsage: this.formatBytes(memUsage.heapUsed)
    };
  }

  private calculateOverallHealth(checks: any): string {
    const statuses = [
      checks.system.status,
      checks.memory.status,
      checks.disk.status,
      checks.process.status
    ];

    if (statuses.includes('error')) return 'error';
    if (statuses.includes('warning')) return 'warning';
    return 'healthy';
  }

  private stopWatcher(watcherId: string): void {
    const watcher = this.watchers.get(watcherId);
    if (watcher) {
      watcher.close();
      this.watchers.delete(watcherId);
    }
  }

  // Cleanup method
  cleanup(): void {
    for (const [id, watcher] of this.watchers) {
      watcher.close();
    }
    this.watchers.clear();

    for (const [id, monitor] of this.monitors) {
      clearTimeout(monitor);
    }
    this.monitors.clear();
  }
}
